import SnapLayout from '@/views/Layout/SnapLayout.vue';
import SnapDesignerLayout from '@/views/Layout/SnapDesignerLayout.vue';
import SnapAuthLayout from '@/views/Layout/SnapAuthLayout.vue';
import SnapRegistrationLayout from '@/views/Layout/SnapRegistrationLayout.vue';
import SnapFormLayout from '@/views/Layout/SnapFormLayout.vue';
import SnapFormLayoutSmall from '@/views/Layout/SnapFormLayoutSmall.vue';

// GeneralViews
import NotFound from '@/views/GeneralViews/NotFoundPage.vue';

const SnapDashboard = () => import(/* webpackChunkName: "dashboard" */ '@/views/Dashboard/SnapDashboard.vue');
const Login = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/Login.vue');
const SnapRegistration = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/SnapRegistration.vue');

const SnapForm = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/SnapForm.vue');
const SnapFormWizard = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/SnapFormWizard.vue');
const SnapFormWizardSmall = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/SnapFormWizardSmall.vue');

const Contacts = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/Contacts.vue');
//const Contact = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/Contact.vue');
//const Documents = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/Documents.vue');
const PhotoBooks = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBooks.vue');
const PhotoBook = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBook.vue');
const PhotoBookDesigner = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBookDesigner.vue');

/** new try  */
const PhotoBookDesignerEasy = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBookDesignerEasy.vue');


const PhotoBookInteriorPreview = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBookInteriorPreview.vue');
const PhotoBookCoverPreview = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBookCoverPreview.vue');

// All roles
const Profile = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/Profile.vue');

// Arist routes
const Home = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/Home.vue');
const PhotoBookWizard = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/PhotoBookWizard.vue');

const SnapProfile = () => import(/* webpackChunkName: "pages" */ '@/views/Pages/SnapProfile.vue');


let snapPagesSmall = {
  path: '/s',
  component: SnapFormLayoutSmall,
  name: 'SnapFormLayoutSmall',
  props: true,
  meta: {
    requiresAuth: false,
  },
  children: [
    {
      path: '/sfws/:token',
      name: 'Snap Form Wizard',
      component: SnapFormWizardSmall,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    { path: '*', component: NotFound }
  ]
};
let snapPages = {
  path: '/s',
  component: SnapFormLayout,
  name: 'SnapFormLayout',
  props: true,
  meta: {
    requiresAuth: false,
  },
  children: [
    {
      path: '/s/:token',
      name: 'Snap Form',
      component: SnapForm,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/sfw/:token',
      name: 'Snap Form Wizard',
      component: SnapFormWizard,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/sfw/:token/:pa_name',
      name: 'Snap Form Wizard',
      component: SnapFormWizard,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/sfw/:token/:pa_name',
      name: 'Snap Form Wizard',
      component: SnapFormWizard,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/sfws/:token',
      name: 'Snap Form Wizard',
      component: SnapFormWizardSmall,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    { path: '*', component: NotFound }
  ]
};


let regPages = {
  path: '/r',
  component: SnapRegistrationLayout,
  name: 'RegistrationLayout',
  props: true,
  meta: {
    requiresAuth: false,
  },
  children: [
    {
      path: '/r/:slug',
      name: 'Registration',
      component: SnapRegistration,
      props: true,
      meta: {
        requiresAuth: false,
      },
    },
    { path: '*', component: NotFound }
  ]
};

let authPages = {
  path: '/',
  component: SnapAuthLayout,
  name: 'Authentication',
  redirect: '/login',
  meta: {
    hideForAuth: true,
    noBodyBackground: true,
  },
  children: [
    {
      path: '/login',
      name: 'Login',
      component: Login,
    },
    { path: '*', component: NotFound }
  ]
};


let snapContactPages = {
  path: '/contacts',
  component: SnapLayout,
  name: 'Contacts',
  redirect: '/contacts/list',
  meta: {
    requiresAuth: true
  },
  children: [
    {
      path: '/contacts/list',
      name: 'Contacts list',
      component: Contacts,
    },
    { path: '*', component: NotFound }
  ]
};

let snapPhotoBookDesignerPages = {
  path: '/photo_books_d',
  component: SnapDesignerLayout,
  name: 'Art Books',
  redirect: '/photo_books_d/list',
  meta: {
    requiresAuth: true
  },
  children: [
    {
      path: '/photo_books_d/:token/creator',
      name: 'show Art Book',
      component: PhotoBookDesigner,
      props: true,
      meta: {
        hideNavbar: true,
        hideSidebar: true,
        hideFooter: true,
        hideHeader: true,
        hideHeader: false,
      },
    },
    {
      path: '/photo_books_d/:token/designer',
      name: 'show Art Book easy',
      component: PhotoBookDesignerEasy,
      props: true,
      meta: {
        hideNavbar: true,
        hideSidebar: true,
        hideFooter: true,
        hideHeader: true,

        //hideNavbar: false,
        //hideSidebar: false,
        //hideFooter: false,
        hideHeader: false,
      },
    },
    {
      path: '/photo_books_d/:token/coverpreview',
      name: 'show Art Book cover preview',
      component: PhotoBookCoverPreview,
      props: true,
      meta: {
        hideHeader: false,
      },
    },
    {
      path: '/photo_books_d/:token/preview',
      name: 'show Art Book preview',
      component: PhotoBookInteriorPreview,
      props: true,
      meta: {
        hideHeader: false,
      },
    },
    { path: '*', component: NotFound }
  ]
};


let snapPhotoBookPages = {
  path: '/photo_books',
  component: SnapLayout,
  name: 'Art Books',
  redirect: '/photo_books/list',
  meta: {
    requiresAuth: true
  },
  children: [
    {
      path: '/photo_books/list',
      name: 'Art Books',
      component: PhotoBooks,
    },
    {
      path: '/photo_books/:token',
      name: 'show Art Book',
      component: PhotoBook,
      props: true,
    },
    {
      path: '/photo_books/:token/creator',
      name: 'show Art Book',
      component: PhotoBookDesigner,
      props: true,
      meta: {
        hideNavbar: true,
        hideSidebar: true,
        hideFooter: true,
        hideHeader: true,

        //hideNavbar: false,
        //hideSidebar: false,
        //hideFooter: false,
        hideHeader: false,
      },
    },
    { path: '*', component: NotFound }
  ]
};
/*
let snapDocumentPages = {
  path: '/documents',
  component: SnapLayout,
  name: 'Dokumente & Downloads',
  redirect: '/documents/list',
  meta: {
    requiresAuth: true
  },
  children: [
    {
      path: '/documents/list',
      name: 'Dokumente',
      component: Documents,
    },
    { path: '*', component: NotFound }
  ]
};*/

let artistPages = {
  path: '/artist',
  component: SnapLayout,
  name: 'Artist',
  redirect: '/artist/home',
  meta: {
    requiresAuth: true
  },
  children: [
    {
      path: '/artist/home',
      name: 'Artist home',
      component: SnapDashboard,
      meta: {
        hideNavbar: false,
        hideSidebar: true,
        hideFooter: true,
      },
    },
    {
      path: '/artist/:token/wizard',
      name: 'Artist Book Wizard',
      component: PhotoBookWizard,
      props: true,
      meta: {
        hideNavbar: false,
        hideSidebar: true,
        hideFooter: false,
        hideHeader: true,
      },
    },
    { path: '*', component: NotFound }
  ]
};

const routes = [
  snapPagesSmall,
  snapPages,
  regPages,
  artistPages,
  snapContactPages,
  snapPhotoBookPages,
  //snapDocumentPages,
  snapPhotoBookDesignerPages,
  {
    path: '/portal',
    component: SnapLayout,
    name: 'snap layout',
    redirect: '/portal/home',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '/portal/home',
        name: 'snap dashboard',
        component: SnapDashboard,
      },
        {
          path: '/portal/profile',
          name: 'snap profile',
          component: SnapProfile,
        },
      { path: '*', component: NotFound }
    ]
  },
  authPages,
];


export default routes;