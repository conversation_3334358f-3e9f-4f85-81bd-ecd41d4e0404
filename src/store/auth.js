/* global localStorage */
import { mapGetters, mapState, mapSetters } from 'vuex'
import Account from '../models/Account'
import * as MutationTypes from './mutation-types'

const state = {
  account: Account.from(localStorage.token)
}

const mutations = {
  [MutationTypes.LOGIN](state) {
    state.account = Account.from(localStorage.token)
  },
  [MutationTypes.LOGOUT](state) {
    state.account = null
  },
  setAccount(state, item) {
    if (item.firstname !== undefined) state.account.firstname = item.firstname;
    if (item.lastname !== undefined) state.account.lastname = item.lastname;
    if (item.email !== undefined) state.account.email = item.email;
    if (item.instagram_account_name !== undefined) state.account.instagram_account_name = item.instagram_account_name;
    if (item.address !== undefined) state.account.address = item.address;
    if (item.profile_img !== undefined) state.account.profile_img = item.profile_img;
  },
}

const getters = {
  currentAccount(state) {
    return state.account
  },
  getAccount(state) {
    return state.account
  },
  currentToken(state) {
    return localStorage.token
  }
}

const actions = {
  login({ commit }) {
    commit(MutationTypes.LOGIN)
  },

  logout({ commit }) {
    commit(MutationTypes.LOGOUT)
  }
}


export default {
  state,
  mutations,
  getters,
  actions
}
