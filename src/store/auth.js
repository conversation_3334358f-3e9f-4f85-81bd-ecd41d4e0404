/* global localStorage */
import { mapGetters, mapState, mapSetters } from 'vuex'
import Account from '../models/Account'
import * as MutationTypes from './mutation-types'

const state = {
  account: Account.from(localStorage.token)
}

const mutations = {
  [MutationTypes.LOGIN](state) {
    state.account = Account.from(localStorage.token)
  },
  [MutationTypes.LOGOUT](state) {
    state.account = null
  },
  setAccount(state, item) {
    console.log(item);
    state.account.firstname = item.firstname;

    state.account.lastname = item.lastname;
    state.account.instagram_account_name = item.instagram_account_name;

  },
}

const getters = {
  currentAccount(state) {
    return state.account
  },
  getAccount(state) {
    return state.account
  },
  currentToken(state) {
    return localStorage.token
  }
}

const actions = {
  login({ commit }) {
    commit(MutationTypes.LOGIN)
  },

  logout({ commit }) {
    commit(MutationTypes.LOGOUT)
  }
}


export default {
  state,
  mutations,
  getters,
  actions
}
