<template>
    <div>
      <div class="header py-7 py-lg-8 pt-lg-9" :class="AppStyle.GRADIENT_STYLE">
        <div class="separator separator-bottom separator-skew zindex-100">
          <svg x="0" y="0" viewBox="0 0 2560 0" preserveAspectRatio="none" version="1.1"
               xmlns="http://www.w3.org/2000/svg">
            <polygon class="fill-default" points="2560 0 2560 100 0 100"></polygon>
          </svg>
        </div>
      </div>
  
      <!-- Page content -->
      <b-container class="mt--8 pb-5">
        <b-row class="justify-content-center">
          <b-col lg="8" md="12">
            <b-card no-body class="border-0 mb-0">
              <b-card-body class="px-lg-5 py-lg-5">
                
                <div class="loader" v-if="loading"></div>

                <div class="text-center text-muted mb-4">
                  <!-- img :src="logo" class="navbar-brand-img" alt="Sidebar logo" width="90%" -->
                </div>



                <div class="text-center text-muted mb-4" v-if="expired">
                  <h1>Registration expired</h1>
                  <p>Your registration link has expired, please contact your publisher representative.</p>
                </div>


                <div class="text-center text-muted mb-4" v-if="!expired">
                  <h1>Your publishing Agreement</h1>
                  <p>Please review and confirm your account data</p>
                </div>





                <validation-observer v-if="!expired" v-slot="{handleSubmit}" ref="formValidator">
                  <b-form role="form" @submit.prevent="handleSubmit(onSubmit)">



                    <b-row>
                  <b-col md="12">
                    <label>Your email address</label>
                    <p>{{ account.email }}</p>
                  </b-col>
                    </b-row>



                <b-row>

                  <b-col md="6">
                    <base-input prepend-icon="fas fa-user" placeholder="Your Name" v-model="account.firstname" name="Strasse" rules="required|alpha_num"></base-input>
                  </b-col>

                  <b-col md="6">
                    <base-input prepend-icon="fas fa-user" placeholder="Your Surname" v-model="account.lastname" name="Nummer" rules="required|alpha_num"></base-input>
                  </b-col>

                </b-row>

                <b-row>

                    <b-col md="6">
                        <base-input prepend-icon="ni ni-lock-circle-open" type="password" placeholder="Password" v-model="account.password" name="Password" rules="required|alpha_num"></base-input>
                    </b-col>

                    <b-col md="6">
                        <base-input prepend-icon="ni ni-lock-circle-open" type="password" placeholder="Password confirmation" v-model="account.password_confirmation" name="Password confirmation" rules="required|alpha_num"></base-input>
                    </b-col>

                </b-row>


                <b-row>

                    <b-col md="6">
                    <base-input name="Country" rules="required">
                      <el-select v-model="account.address.country" filterable placeholder="Country" rules="required">
                        <el-option v-for="option in countries"
                                   :key="option.label"
                                   :label="option.label"
                                   :value="option.value">
                        </el-option>
                      </el-select>
                    </base-input>
                  </b-col>

                    <b-col md="6">
                        <base-input prepend-icon="fas fa-map-marker" placeholder="Street, number" v-model="account.address.street" name="Street an number confirmation" rules="required"></base-input>
                    </b-col>

                </b-row>

                <b-row>

                    <b-col md="6">
                        <base-input prepend-icon="fas fa-map-marker" placeholder="Zip" v-model="account.address.zip" name="Zip" rules="required|alpha_num"></base-input>
                    </b-col>

                    <b-col md="6">
                        <base-input prepend-icon="fas fa-map-marker" placeholder="City" v-model="account.address.city" name="City" rules="required|alpha_num"></base-input>
                    </b-col>

                </b-row>

                <b-row>

                <b-col md="6">
                    <base-input prepend-icon="fas fa-phone" placeholder="+1 - including country code" v-model="account.address.phone" name="Phone" rules="required|phone"></base-input>
                </b-col>

                </b-row>

                <b-row>
                    <b-col md="12">

                      <base-input :rules="{ required: { allowFalse: false } }" name="Publishing agreement">
                        <b-form-checkbox v-model="confirmcontract">
                          <span class="text-muted">I hereby confirm the publishing agreement for my book SNCB00021 <a href="#!" @click="retrieveDocument()">download Publishing Agreement here </a></span>
                        </b-form-checkbox>
                      </base-input>

                    </b-col>
                </b-row>
                   
                    <div class="text-center">
                      <base-button :type="AppStyle.BUTTON_STYLE_S" native-type="submit" class="my-4">Start my publication</base-button>
                    </div>
                  </b-form>
                </validation-observer>
              </b-card-body>
            </b-card>
          </b-col>
        </b-row>
      </b-container>
    </div>
  </template>
  
  <script>
  import { mapGetters, mapState } from 'vuex';
  import AppStyle from '@/plugins/appStyle';
  import { Select, Option } from 'element-ui'
  import flatPicker from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { extend } from "vee-validate";
import { required, email, min } from "vee-validate/dist/rules";
import { configure } from 'vee-validate';
import { localize } from 'vee-validate';
import de from 'vee-validate/dist/locale/uk.json';

localize('us');

extend('phone', {
  validate: value => {
    return /^[0-9+\-\s()]*$/.test(value);
  },
  message: 'The {_field_} field may only contain numbers, plus signs, hyphens, spaces and parentheses'
});

  export default {
    mixins: [AppStyle.Mixin],
      name: 'SnapRegistration',
      components: {
      [Select.name]: Select,
      [Option.name]: Option,
  },
      data() {
        return {
          loading: false,
          confirmcontract: false,
          document: [],
          expired: false,
          account: {
            email: '',
            password: '',
            password_confirmation: '',
            firstname: '',
            lastname: '',
            instagram_profile: '',
            address: {
                country: '',
                street: '',
                zip: '',
                city: '',
            },
            acctok: '',
            projtok: '',
            
          },
          countries: [
            {value: "AF", label: "Afghanistan"},
{value: "AX", label: "Åland Islands"},
{value: "AL", label: "Albania"},
{value: "DZ", label: "Algeria"},
{value: "AS", label: "American Samoa"},
{value: "AD", label: "Andorra"},
{value: "AO", label: "Angola"},
{value: "AI", label: "Anguilla"},
{value: "AQ", label: "Antarctica"},
{value: "AG", label: "Antigua and Barbuda"},
{value: "AR", label: "Argentina"},
{value: "AM", label: "Armenia"},
{value: "AW", label: "Aruba"},
{value: "AU", label: "Australia"},
{value: "AT", label: "Austria"},
{value: "AZ", label: "Azerbaijan"},
{value: "BS", label: "Bahamas"},
{value: "BH", label: "Bahrain"},
{value: "BD", label: "Bangladesh"},
{value: "BB", label: "Barbados"},
{value: "BY", label: "Belarus"},
{value: "BE", label: "Belgium"},
{value: "BZ", label: "Belize"},
{value: "BJ", label: "Benin"},
{value: "BM", label: "Bermuda"},
{value: "BT", label: "Bhutan"},
{value: "BO", label: "Bolivia"},
{value: "BQ", label: "Bonaire, Sint Eustatius and Saba"},
{value: "BA", label: "Bosnia and Herzegovina"},
{value: "BW", label: "Botswana"},
{value: "BV", label: "Bouvet Island"},
{value: "BR", label: "Brazil"},
{value: "IO", label: "British Indian Ocean Territory"},
{value: "BN", label: "Brunei Darussalam"},
{value: "BG", label: "Bulgaria"},
{value: "BF", label: "Burkina Faso"},
{value: "BI", label: "Burundi"},
{value: "CV", label: "Cabo Verde"},
{value: "KH", label: "Cambodia"},
{value: "CM", label: "Cameroon"},
{value: "CA", label: "Canada"},
{value: "KY", label: "Cayman Islands"},
{value: "CF", label: "Central African Republic"},
{value: "TD", label: "Chad"},
{value: "CL", label: "Chile"},
{value: "CN", label: "China"},
{value: "CX", label: "Christmas Island"},
{value: "CC", label: "Cocos (Keeling) Islands"},
{value: "CO", label: "Colombia"},
{value: "KM", label: "Comoros"},
{value: "CG", label: "Congo"},
{value: "CD", label: "Congo, The Democratic Republic of the"},
{value: "CK", label: "Cook Islands"},
{value: "CR", label: "Costa Rica"},
{value: "CI", label: "Côte d'Ivoire"},
{value: "HR", label: "Croatia"},
{value: "CW", label: "Curaçao"},
{value: "CY", label: "Cyprus"},
{value: "CZ", label: "Czechia"},
{value: "DK", label: "Denmark"},
{value: "DJ", label: "Djibouti"},
{value: "DM", label: "Dominica"},
{value: "DO", label: "Dominican Republic"},
{value: "EC", label: "Ecuador"},
{value: "EG", label: "Egypt"},
{value: "SV", label: "El Salvador"},
{value: "GQ", label: "Equatorial Guinea"},
{value: "ER", label: "Eritrea"},
{value: "EE", label: "Estonia"},
{value: "SZ", label: "Eswatini"},
{value: "ET", label: "Ethiopia"},
{value: "FK", label: "Falkland Islands (Malvinas)"},
{value: "FO", label: "Faroe Islands"},
{value: "FJ", label: "Fiji"},
{value: "FI", label: "Finland"},
{value: "FR", label: "France"},
{value: "GF", label: "French Guiana"},
{value: "PF", label: "French Polynesia"},
{value: "TF", label: "French Southern Territories"},
{value: "GA", label: "Gabon"},
{value: "GM", label: "Gambia"},
{value: "GE", label: "Georgia"},
{value: "DE", label: "Germany"},
{value: "GH", label: "Ghana"},
{value: "GI", label: "Gibraltar"},
{value: "GR", label: "Greece"},
{value: "GL", label: "Greenland"},
{value: "GD", label: "Grenada"},
{value: "GP", label: "Guadeloupe"},
{value: "GU", label: "Guam"},
{value: "GT", label: "Guatemala"},
{value: "GG", label: "Guernsey"},
{value: "GN", label: "Guinea"},
{value: "GW", label: "Guinea-Bissau"},
{value: "GY", label: "Guyana"},
{value: "HT", label: "Haiti"},
{value: "HM", label: "Heard Island and McDonald Islands"},
{value: "VA", label: "Holy See (Vatican City State)"},
{value: "HN", label: "Honduras"},
{value: "HK", label: "Hong Kong"},
{value: "HU", label: "Hungary"},
{value: "IS", label: "Iceland"},
{value: "IN", label: "India"},
{value: "ID", label: "Indonesia"},
{value: "IQ", label: "Iraq"},
{value: "IE", label: "Ireland"},
{value: "IM", label: "Isle of Man"},
{value: "IL", label: "Israel"},
{value: "IT", label: "Italy"},
{value: "JM", label: "Jamaica"},
{value: "JP", label: "Japan"},
{value: "JE", label: "Jersey"},
{value: "JO", label: "Jordan"},
{value: "KZ", label: "Kazakhstan"},
{value: "KE", label: "Kenya"},
{value: "KI", label: "Kiribati"},
{value: "KW", label: "Kuwait"},
{value: "KG", label: "Kyrgyzstan"},
{value: "LA", label: "Lao People's Democratic Republic"},
{value: "LV", label: "Latvia"},
{value: "LB", label: "Lebanon"},
{value: "LS", label: "Lesotho"},
{value: "LR", label: "Liberia"},
{value: "LY", label: "Libya"},
{value: "LI", label: "Liechtenstein"},
{value: "LT", label: "Lithuania"},
{value: "LU", label: "Luxembourg"},
{value: "MO", label: "Macao"},
{value: "MG", label: "Madagascar"},
{value: "MW", label: "Malawi"},
{value: "MY", label: "Malaysia"},
{value: "MV", label: "Maldives"},
{value: "ML", label: "Mali"},
{value: "MT", label: "Malta"},
{value: "MH", label: "Marshall Islands"},
{value: "MQ", label: "Martinique"},
{value: "MR", label: "Mauritania"},
{value: "MU", label: "Mauritius"},
{value: "YT", label: "Mayotte"},
{value: "MX", label: "Mexico"},
{value: "FM", label: "Micronesia, Federated States of"},
{value: "MD", label: "Moldova"},
{value: "MC", label: "Monaco"},
{value: "MN", label: "Mongolia"},
{value: "ME", label: "Montenegro"},
{value: "MS", label: "Montserrat"},
{value: "MA", label: "Morocco"},
{value: "MZ", label: "Mozambique"},
{value: "MM", label: "Myanmar"},
{value: "NA", label: "Namibia"},
{value: "NR", label: "Nauru"},
{value: "NP", label: "Nepal"},
{value: "NL", label: "Netherlands"},
{value: "NC", label: "New Caledonia"},
{value: "NZ", label: "New Zealand"},
{value: "NI", label: "Nicaragua"},
{value: "NE", label: "Niger"},
{value: "NG", label: "Nigeria"},
{value: "NU", label: "Niue"},
{value: "NF", label: "Norfolk Island"},
{value: "MK", label: "North Macedonia"},
{value: "MP", label: "Northern Mariana Islands"},
{value: "NO", label: "Norway"},
{value: "OM", label: "Oman"},
{value: "PW", label: "Palau"},
{value: "PS", label: "Palestine, State of"},
{value: "PA", label: "Panama"},
{value: "PG", label: "Papua New Guinea"},
{value: "PY", label: "Paraguay"},
{value: "PE", label: "Peru"},
{value: "PH", label: "Philippines"},
{value: "PN", label: "Pitcairn"},
{value: "PL", label: "Poland"},
{value: "PT", label: "Portugal"},
{value: "PR", label: "Puerto Rico"},
{value: "QA", label: "Qatar"},
{value: "RE", label: "Réunion"},
{value: "RO", label: "Romania"},
{value: "RW", label: "Rwanda"},
{value: "BL", label: "Saint Barthélemy"},
{value: "SH", label: "Saint Helena, Ascension and Tristan da Cunha"},
{value: "KN", label: "Saint Kitts and Nevis"},
{value: "LC", label: "Saint Lucia"},
{value: "MF", label: "Saint Martin (French part)"},
{value: "PM", label: "Saint Pierre and Miquelon"},
{value: "VC", label: "Saint Vincent and the Grenadines"},
{value: "WS", label: "Samoa"},
{value: "SM", label: "San Marino"},
{value: "ST", label: "Sao Tome and Principe"},
{value: "SA", label: "Saudi Arabia"},
{value: "SN", label: "Senegal"},
{value: "RS", label: "Serbia"},
{value: "SC", label: "Seychelles"},
{value: "SL", label: "Sierra Leone"},
{value: "SG", label: "Singapore"},
{value: "SX", label: "Sint Maarten (Dutch part)"},
{value: "SK", label: "Slovakia"},
{value: "SI", label: "Slovenia"},
{value: "SB", label: "Solomon Islands"},
{value: "SO", label: "Somalia"},
{value: "ZA", label: "South Africa"},
{value: "GS", label: "South Georgia and the South Sandwich Islands"},
{value: "KR", label: "South Korea"},
{value: "ES", label: "Spain"},
{value: "LK", label: "Sri Lanka"},
{value: "SD", label: "Sudan"},
{value: "SR", label: "Suriname"},
{value: "SJ", label: "Svalbard and Jan Mayen"},
{value: "SE", label: "Sweden"},
{value: "CH", label: "Switzerland"},
{value: "TW", label: "Taiwan"},
{value: "TJ", label: "Tajikistan"},
{value: "TZ", label: "Tanzania"},
{value: "TH", label: "Thailand"},
{value: "TL", label: "Timor-Leste"},
{value: "TG", label: "Togo"},
{value: "TK", label: "Tokelau"},
{value: "TO", label: "Tonga"},
{value: "TT", label: "Trinidad and Tobago"},
{value: "TN", label: "Tunisia"},
{value: "TR", label: "Türkiye"},
{value: "TM", label: "Turkmenistan"},
{value: "TC", label: "Turks and Caicos Islands"},
{value: "TV", label: "Tuvalu"},
{value: "UG", label: "Uganda"},
{value: "UA", label: "Ukraine"},
{value: "AE", label: "United Arab Emirates"},
{value: "GB", label: "United Kingdom"},
{value: "US", label: "United States"},
{value: "UM", label: "United States Minor Outlying Islands"},
{value: "UY", label: "Uruguay"},
{value: "UZ", label: "Uzbekistan"},
{value: "VU", label: "Vanuatu"},
{value: "VE", label: "Venezuela"},
{value: "VN", label: "Vietnam"},
{value: "VG", label: "Virgin Islands, British"},
{value: "VI", label: "Virgin Islands, U.S."},
{value: "WF", label: "Wallis and Futuna"},
{value: "EH", label: "Western Sahara"},
{value: "YE", label: "Yemen"},
{value: "ZM", label: "Zambia"},
{value: "ZW", label: "Zimbabwe"},
            ],
        };
      },
      props:{
        logo: {
            type: String,
            default: AppStyle.LOGO,
            description: 'Sidebar app logo'
        },
        slug: {
              type: String,
          },
      },
    mounted () {
        this.retrieveRegistrationData();
      //this.checkCurrentLogin()
    },
    updated () {
      //this.checkCurrentLogin()
    },
    methods: {
        onSubmit() {
            console.log("SUBMIT NOW ---_> OnSubmit");
            this.registerAndPublish();
     // this.$api.login(this.model.email, this.model.password).then(request => this.loginSuccessful(request)).catch(() => this.loginFailed())
    },
        handleSubmit(){
            // check if all is there
            console.log("SUBMIT NOW");
        },
        registerAndPublish() {
        this.loading = true;
        this.$api.setRegistrationData(this.account, this.acctok, this.projtok, this.confirmcontract)
          .then((response) => {
            console.log(response);
            // hiernach redirect falls erfolgreich... und nutzer einloggen
            if(response.data.token){
                // we have token redirect
                localStorage.token = response.data.token; //req.data.token
                this.$store.dispatch('login')
                this.$router.replace(this.$route.query.redirect || '/portal');
            }
            else{
                // error...hmmm what now?
            }
           // this.account = response.data;
          })
          .catch((e) => {
            console.log(e);
          }).finally(() => {
            this.loading = false
          });
      },

        retrieveRegistrationData() {
        this.loading = true;
        this.$api.getRegistrationData(this.slug)
          .then((response) => {
            console.log(response);
            if(response.data.code == 502){
                this.expired = true;
            }
            if(response.data.code == 400){
                this.expired = true;
            }
            this.account = response.data.account;
            this.projtok = response.data.projtok;
            this.acctok = response.data.acctok;
          })
          .catch((e) => {
            console.log(e);
          }).finally(() => {
            this.loading = false
          });
      },

      retrieveDocument() {
        this.loading = true;
        this.$api.getDocument("publishing_contract")
          .then((response) => {
            this.document = response.data;
          })
          .catch((e) => {
            console.log(e);
          }).finally(() => {
            this.loading = false;
            this.retrieveDocumentFile();
          });
      },
      retrieveDocumentFile() {
       // this.retrieveDocument();
        this.loading = true;
        this.$api.getDocumentFile("publishing_contract")
          .then((response) => {
            console.log(response);
            this.forceFileDownload(response, this.document.filename);
          })
          .catch((e) => {
            console.log(e);
          }).finally(() => {
            this.loading = false
          });
      },
      forceFileDownload(response, title) {
        console.log(title)
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', title)
        document.body.appendChild(link)
        link.click()
      },
      formatDate(date) {
        return moment(date).format("Do MMM YYYY")
      },

    },
  }
  </script>
  
