<template>

<div class="content">


    <div class="navbar">
		<!-- a href="javascript: alert('Nach hause telefonieren');" target="_self" class="logo"><img src="/images/logo.png"></a -->

        <div role="group" aria-label="Basic example" class="btn-group">
            <button type="button" @click="togglePageList()" :class="{ active: showPageList }" class="btn btn-sm base-button btn-secondary btn-icon btn-fab">
                <span class="btn-inner--icon">
                    <i class="ni ni-book-bookmark"></i>
                </span>
                <!-- span class="btn-inner--text">Pages</span -->
            </button>
            <button type="button" @click="toggleImageList()" :class="{ active: showImageList }" class="btn btn-sm base-button btn-secondary btn-icon btn-fab">
                <span class="btn-inner--icon">
                    <i class="ni ni-image"></i>
                </span>
                <!-- span class="btn-inner--text">Images</span -->
            </button>
        </div>

        <div>
            {{ currentPhotoBook.title }} by {{ currentPhotoBook.photographer }}
            
    <button @click="addTextElement">Add Text</button>
    <button >-</button>
    <button >+</button>
    PAGE LL: {{ currentPhotoBookPage.token }}
    PAGE SELECTED: {{ currentPhotoBook.selected_photo_book_page }} {{ selectedPage }}
  </div>

		<ul class="nav">
			<!-- li><b-button @click="toggleImageList()">Images</b-button></li>
			<li><b-button @click="togglePageList()">Pages</b-button></li -->
			<li><b-button @click="toggleOptions()">Options</b-button></li>
		</ul>	

	</div>

    
    <div class="dasgrosseganze">

        <photo-book-designer-side-bar :photoBookId="this.token" v-if="showImageList" @imageselected="imageSelected"></photo-book-designer-side-bar>
        <!-- photo-book-designer-image-list :photoBookId="this.token" v-if="showImageList" @imageselected="imageSelected"></photo-book-designer-image-list -->
        <!-- photo-book-designer-page-list :photoBookId="this.token" v-if="showPageList"></photo-book-designer-page-list -->

        <div class="docviewwrapper"> 
			<div class="docview" id="docview" ref="docview">

                <!-- photo-book-designer-page ref="pageLeft" v-if="!loading" :klass="'page_s_l page_left'" :photoBookId="this.token" token="ce461a41-fd89-4e50-9391-ccdfa2640050"></photo-book-designer-page>
                <photo-book-designer-page ref="pageRight"  v-if="!loading" :klass="'page_s_r page_right'" :photoBookId="this.token" token="ce461a41-fd89-4e50-9391-ccdfa2640050"></photo-book-designer-page>
            -->
                <photo-book-designer-page ref="pageLeft" @canvasElementTouched="canvasElementTouched" @selectedPageChanged="selectedPageChanged" v-if="!pagesloading" :klass="'page_s_l page_left'" :photoBookId="this.token"  :page="currentPhotoBookPages[0]" :token="currentPhotoBookPages[0].token"></photo-book-designer-page>
                <photo-book-designer-page ref="pageRight" @canvasElementTouched="canvasElementTouched" @selectedPageChanged="selectedPageChanged" v-if="!pagesloading" :klass="'page_s_r page_righ'" :photoBookId="this.token" :page="currentPhotoBookPages[1]" :token="currentPhotoBookPages[1].token"></photo-book-designer-page>

			</div>	
		</div>	

        <photo-book-designer-image-list :photoBookId="this.token" v-if="showImageList" @imageselected="imageSelected"></photo-book-designer-image-list>

        <div class="toolbar" v-if="showOptions">
			<small>Page {{ selectedPage }}</small>

            <div class="thumbwrapper">
                OPTIONS
				<a href="#" class="thumb"><img src="/images/thumb01.jpg"></a>
			</div>

            <div class="fontoptions" ref="fontoptions" v-if="showFontOptions">
                <base-input label="Basic select">
                        <select class="form-control" id="exampleFormControlSelect1">
                        <option>1</option>
                        <option>2</option>
                        <option>3</option>
                        <option>4</option>
                        <option>5</option>
                        </select>
                </base-input>
                <base-input type="color" value="#5e72e4" id="example-color-input" @input="setPageBackgroundColor"/>
            </div>

            <div class="textoptions" ref="textoptions" v-if="showTextOptions">
                Options for selected text
            </div>

            <div class="rectoptions" ref="rectoptions" v-if="showRectOptions">
                Options for selected rect
            </div>

            <div class="imageoptions" ref="imageoptions" v-if="showImageOptions">
                Options for selected image
                Center the image!
                <button @click="centerImage()">CENTER</button>
                <button @click="centerObject()">CENTER2</button>
            </div>

            <div class="generaloptions" ref="generaloptions" v-if="showGeneralOptions">
                <button @click="setPageBackgroundColor()">PAGEBG-BLK</button>
                <button @click="centerTop()">CENTER-TOP</button>
                <button @click="centerObject()">CENTER-HV</button>
                <button @click="centerHorizontally()">CENTER-H</button>
                <button @click="centerVertically()">CENTER-V</button>
                General options for selected object
                color: 

            </div>

            <div class="pagelayers" ref="pagelayers" v-if="showLayerOptions">
                Layeroptions
            </div>

		</div>

    </div>

    </div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'
import PhotoBookImageList from '../cscomponents/PhotoBookImageList';
import PhotoBookImageUploader from '../cscomponents/PhotoBookImageUploader';

//import PhotoBookDesignerSideBar from '../cscomponents/bookcreator/PhotoBookDesignerSideBar';
//import PhotoBookDesignerPage from '../cscomponents/bookcreator/PhotoBookDesignerPage';
//import PhotoBookDesignerPageList from '../cscomponents/bookcreator/PhotoBookDesignerPageList';
//import PhotoBookDesignerImageList from '../cscomponents/bookcreator/PhotoBookDesignerImageList';

import PhotoBookPage from '../cscomponents/PhotoBookPage';
import PhotoBookPageList from '../cscomponents/PhotoBookPageList';

import moment from 'moment'
import AppStyle from '@/plugins/appStyle';

import DropzoneFileUpload from '@/components/Inputs/DropzoneFileUpload'


export default {
    mixins: [AppStyle.Mixin],
    name: "photo-book-designer-easy",
    components: {
        PhotoBookDesignerSideBar,
        // PhotoBookDesignerImageList,
        PhotoBookDesignerPageList,
        PhotoBookImageUploader,
        PhotoBookPage,
        PhotoBookDesignerPage,
        DropzoneFileUpload,
    },
    data() {
        return {
            selectedPage: '',
            selectedPageUi: '',
            selectedObject: '',
            loading: true,
            pagesloading: true,
            photoBook: null,
            photoBookPages: [],
            showPageList: false,
            showOptions: false,
            showImageList: false,

            showFontOptions: true,
            showImageOptions: true,
            showLayerOptions: true,
            showRectOptions: true,
            showTextOptions: true,
            showGeneralOptions: true,

            vueCanvas: '',
            rectWidth: 0,

            inputs: {
                fileSingle: '',
            },
            rows: 100,
         perPage: 1,
         currentPage: 2,
        }
    },
    props: {
        token: String,
        tab: String,
    },
    methods: {
        canvasElementTouched(obj){

            this.selectedObject = obj;
            this.setActivePage();
            console.log("Elemtouched");
            console.log(obj);
            console.log("Elemtouched");

            this.showOptions = true;

            this.showTextOptions = false;
            this.showRectOptions = false;
            this.showImageOptions = false;

            if(obj.target.get('type') == "i-text"){
                this.showTextOptions = true;
            }
            if(obj.target.get('type') == "rect"){
                this.showRectOptions = true;
            }
            if(obj.target.get('type') == "image"){
                this.showImageOptions = true;
            }

        },

        setActivePage(page = null){
            this.selectedPageUi = this.selectedObject.target.canvas.contextContainer.canvas.parentNode.parentNode;


            console.log("PAGELEFT -> "+this.$refs.pageLeft);
            console.log(this.$refs.pageLeft);
            // remove active classes from all pages
            this.$refs.pageLeft.$el.className = "page_s_l page_left";
            this.$refs.pageRight.$el.className = "page_s_r page_right";

            this.selectedPageUi.className += " page_selected";
        },

        /** SNAP positioning starts here */
        centerTop(obj = null){
           console.log("Center TOP -> "+this.selectedObject);

           this.setObjectPosition(new fabric.Point(100, 100), this.selectedObject);
           
        },

        setObjectPosition(pos, obj = null){
            let canvas = this.selectedObject.target.canvas;

            var pos = new fabric.Point(0, 0);
            obj.target.setPositionByOrigin(pos, "center", "top");

            canvas.requestRenderAll(); 
        },

        centerAll(obj = null){
            console.log("Selected Object -> "+this.selectedObject);
           let canvas = this.selectedObject.target.canvas; 
            var gfg = new fabric.ActiveSelection(canvas.getObjects(), { }); 

            canvas.setActiveObject(gfg); 
            canvas.requestRenderAll(); 
            canvas.centerObject(gfg); 
            console.log(gfg.getCenterPoint())   
            canvas.requestRenderAll(); 
        },

        centerHorizontally(obj = null){
            console.log("Selected Object -> "+this.selectedObject);
           let canvas = this.selectedObject.target.canvas; //this.$refs.pageLeft.getCanvas();
            let centerPoint = canvas.getCenterPoint();
            this.selectedObject.target.centerH();
            //setPositionByOrigin(centerPoint, 'center');
            canvas.requestRenderAll(); 
        },

        centerVertically(obj = null){
            console.log("Selected Object -> "+this.selectedObject);
           let canvas = this.selectedObject.target.canvas; //this.$refs.pageLeft.getCanvas();
            let centerPoint = canvas.getCenterPoint();
            this.selectedObject.target.centerV();
            //setPositionByOrigin(centerPoint, 'center');
            canvas.requestRenderAll(); 
        },

        centerObject(obj = null){
            console.log("Selected Object -> "+this.selectedObject);
           let canvas = this.selectedObject.target.canvas; //this.$refs.pageLeft.getCanvas();
            let centerPoint = canvas.getCenterPoint();
            this.selectedObject.target.setPositionByOrigin(centerPoint, 'center', 'center');
            canvas.requestRenderAll(); 
//            image.setPositionByOrigin(centerPoint, 'center', 'center');

        },

        centerImage(obj = null){
            console.log("Selected Object -> "+this.selectedObject);
           let canvas = this.selectedObject.target.canvas; //this.$refs.pageLeft.getCanvas();
           
            //canvas.centerObject(obj);  
           // console.log();
            var gfg = new fabric.ActiveSelection(canvas.getObjects(), { 
            }); 
            console.log(gfg);

            var gfg = new fabric.ActiveSelection(canvas.getObjects(), { 
      }); 
      canvas.setActiveObject(gfg); 
      canvas.requestRenderAll(); 
      canvas.centerObject(gfg); 
      console.log(gfg.getCenterPoint())  

            //console.log(centerPoint);
            //var centerPoint = rect.getCenterPoint();

            //set the image with the center on that point

            obj.setPositionByOrigin(centerPoint, 'center', 'center');
//            image.setPositionByOrigin(centerPoint, 'center', 'center');

        },
        /** snap positioning ends here */


        /** snap coloring */
        setPageBackgroundColor(color = 'black'){


            console.log("THIS -> "+this);
            console.log("THIS VALUE -> "+color);
           console.log("Selected Object setPageBackgroundColor -> "+this.selectedObject);
           let canvas = this.selectedObject.target.canvas; 

           

           let object = canvas.item(0)
           console.log("GET OBJ FROM INDEX ->"+object);
           console.log(object);

           if(object == "#<fabric.Rect>"){
            console.log("SETTING NEW COLOR TO OLD LAYER");
            object.set("fill", color);
           }
           else{
            console.log("ADD NEW LAYER WITH BG COLOR");

            const rect = new fabric.Rect({
                fill: color,
                width: canvas.width,
                height: canvas.height,
                selectable: false,
            });

            canvas.add(rect);
            rect.sendBackwards();


           }
           //console.log(object.getType());
           //canvas.setActiveObject(object);
           
//            object.fill = 'green';



            canvas.requestRenderAll(); 
        },
        /** snap coloring end */


        selectedPageChanged(page = null){
            console.log(page);
            console.log(this.currentPhotoBook.selected_photo_book_page);
            this.selectedPage = this.currentPhotoBook.selected_photo_book_page;
        },

        togglePageOptions(){

        },

        imageSelected(img){
            console.log("SELECTED iamge"+img);
            console.log("SELECTED iamge"+img.id);
            this.$refs.pageLeft.addImage(img);
        },
        retrievePhotoBook() {
            this.loading = true;
            this.$store.dispatch('fetchPhotoBook', this.token)
            .then(() => {
                console.log(this.$store.getters.currentPhotoBook);
            })
            .catch((error) => {
                console.error(error);
                this.loading = true;
            }).finally(() => {
                    

                    console.log("The current book");
                console.log(this.currentPhotoBook);
                console.log(this.$store.getters.currentPhotoBook);
                console.log("The current book end");


                this.retrievePhotoBookPages();

                this.loading = false;

                });


        },
        retrievePhotoBookPages() {
            this.pagesloading = true;
            this.$store.dispatch('fetchPhotoBookPages', this.$store.getters.currentPhotoBook.id)
            .then(() => {
                console.log("Getters pages:"+this.$store.getters.currentPhotoBookPages);
                console.log(this.$store.getters.currentPhotoBookPages);
                console.log("State pages:"+this.$store.state.photoBookPages);
            })
            .catch((error) => {
                console.error(error);
                this.pagesloading = true;
            }).finally(() => {
                console.log("Getters pages:"+this.$store.getters.currentPhotoBookPages);
                console.log(this.$store.getters.currentPhotoBookPages);
                console.log("State pages:"+this.$store.state.photoBookPages);
                    this.pagesloading = false;

                    console.log(this.currentPhotoBookPages[0]);
            });
        },
        toggleImageList() {
            if (this.showImageList) {
                this.showImageList = false;
            }
            else {
                this.showImageList = true;
            }
        },

        togglePageList() {
            if (this.showPageList) {
                this.showPageList = false;
            }
            else {
                this.showPageList = true;
            }
        },

        toggleOptions() {
            if (this.showOptions) {
                this.showOptions = false;
            }
            else {
                this.showOptions = true;
            }
        },

        addedfile(){
            console.log("ADDEDFILE");
            console.log(this.inputs);
            console.log(this.inputs.fileSingle[0]);
            console.log(this.inputs.fileSingle[0].dataURL);
        },
        
        handleOrientationSet(){

        },

        pageClass(){
            return this.currentPhotoBook.orientation;
        },


        addPage(){
        },

        addTextElement(){
            this.$refs.pageLeft.addText();

        },
        

        /**
         * Conserve aspect ratio of the original region. Useful when shrinking/enlarging
         * images to fit into a certain area.
         *
         * @param {Number} srcWidth width of source image
         * @param {Number} srcHeight height of source image
         * @param {Number} maxWidth maximum available width
         * @param {Number} maxHeight maximum available height
         * @return {Object} { width, height }
         */
         calculateAspectRatioFit(srcWidth, srcHeight, maxWidth, maxHeight) {

        var ratio = Math.min(maxWidth / srcWidth, maxHeight / srcHeight);

        return { width: srcWidth*ratio, height: srcHeight*ratio };
        },
        
    },
    computed: {
        ...mapGetters({
            currentAccount: 'currentAccount',
            currentSettings: 'currentSettings',
            currentPhotoBook: 'currentPhotoBook',
            currentPhotoBookPage: 'currentPhotoBookPage',
            currentPhotoBookPages: 'currentPhotoBookPages',
            //selectedPage: 'currentPhotoBook.selected_photo_book_page',
        }),
        ...mapActions({
            fetchPhotoBook: 'fetchPhotoBook',
        }),
    },
    mounted() {
        this.retrievePhotoBook();
        
        

    },
    created() {
    },
}
</script>
<style scoped>

* {
    margin: 0;
    /*
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    */
    vertical-align: baseline;
    box-sizing: border-box;
    color: inherit;
}

html,body {
    height: 100vh;
	width: 100vw; 
	/* 
	background: #f8f9fa;
	color: #344767;
	
	font-size: 16px;
	line-height: 1.4em;
	font-family: "Poppins", sans-serif;
	font-weight: 300;
    */
	position: relative;
}

a, a:link, a:visited{
	text-decoration: none;
	color: #344767;
}


/* Navbar */
.navbar{
	position: fixed;
	height: 70px;
	background: #FFF;
	/* 
    border-radius: 1rem; 
    */
	left: 0px;
	right: 0px;
	top: 0px;
	/*
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
	/* 
    padding: 2rem;
	*/
    display: flex;
	flex-direction: row;
	justify-content: space-between; 
	/*
    align-items: center;
	*/
    z-index: 21;
}
.navbar a.logo{
	width: 160px; 
	img{
		max-width: 100%;
		height: auto;
	}
}
.navbar ul.nav{
	list-style-type: none;
	display: flex;
	flex-direction: row;
	gap: 15px;
	align-items: center;
	justify-content: flex-end;
	margin: 0;
	padding: 0;
}
.navbar ul.nav li{
	display: inline-flex;
	margin: 0;
	padding: 0;
}
.navbar img{
	max-width: 100%;
	height: auto;
}
/* Struktur */
.dasgrosseganze{
	display: flex;
	flex-direction: row;
	justify-content: space-beetwen; 
	height: 100vh;
	width: 100vw;
	padding-top: 100px;
}
.sidebar{
	position: absolute;
	left: 0px;
	bottom: 0px;
	top: 70px;
	width: 75px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 1rem;
	z-index: 20;
	background: #FFF;
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.sidebar img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}

.sidebar_img{
	position: absolute;
	left: 70px;
    right: 0px;
	bottom: 0px;
	
	
	overflow-x: auto;
	overflow-y: auto;
    height: 150px;
   
    padding-right: 70px;
	/*
    padding: 1rem;
	*/
    z-index: 20;
	background: #FFF;
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.sidebar_img img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}
.photo_book_image_list_group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  padding-left: 0;
  margin-bottom: 0;
}

.toolbar{
	position: absolute;
	right: 0px;
	bottom: 0px;
	top: 70px;
	width: 200px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 1rem;
	z-index: 20;
	background: #FFF;
	
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.toolbar img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}



.docviewwrapper{
	position: fixed;
	/*
    left: 304px;
	right: 150px;
	*/
    left: 200px;
	right: 200px;
    bottom: 1rem;
	top: 80px;
	/*
	right: 1rem;
	*/
}
@media screen and (min-width: 992px){
	.docview{
		display: flex;
		flex-direction: row;
		gap: 0px;
		justify-content: center;
		align-content: center;
		padding: 30px;
		height: 100%;
	}

    .page_selected{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: left;
		justify-content: left;
		padding: 0px;
        border: #344767 10px outset;
	}

	.page_left{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: right;
		justify-content: right;
		padding: 0px;
	}

	.page_right{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: left;
		justify-content: left;
		padding: 0px;
	}

	.docview .page_left img{
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
		display: block;
		max-width: 100%;
		max-height: 100%;
	}

	.docview .page_right img{
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
		display: block;
		max-width: 100%;
		max-height: 100%;
	}
	.docview a{
		display: flex;
		height: 100%;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}
}
@media screen and (max-width: 992px){
	.docviewwrapper{
		overflow-x: hidden;
		overflow-y: auto;
		bottom: 0;
		top: 90px;
	}
	.docview {
		display: block;
		padding: 0 20px;
	}
	.docview a{
		display: block;
		padding: 20px;
	}
	.docview{
		flex-wrap: wrap;
	}
	.docview img{
		display: block;
		max-width: 100%;
		height: auto;
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
	}
} 



.spine{
    background-color: pink;
    width: 100px;
}
.imglist {
    /*
    background-color: red;
    */
    overflow-y: scroll;
    max-height: 80vh;
    padding-bottom: 0px;
    margin-left: 0px;
}

.maxih {
    background-color: red;
    max-height: 80vh;
    min-height: 80vh;
    padding-bottom: 0px;
    margin-left: 0px;
}

.photo_book_image_list_wrapper {
    padding-top: 0.5rem;
    padding-right: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.landscape {
    width: 148mm;
    height: 105mm;

    width: 222mm;
    height: 157mm;
}

.portrait {
    height: 297mm;
    width: 210mm;

    width: 157mm;
    height: 222mm;


}

.printpage {
    background-size: contain;
    background-repeat: no-repeat;
}

.page_framed {
    background-size: contain;
    background-repeat: no-repeat;
    padding: 1cm;
}

.page_contain {
    background-size: contain;
    background-repeat: no-repeat;
}

.page_cover {
    background-size: cover;
}

.flipbook-bg {
  width: 80vw;
  height: 80vh;
}




#c {
  height: 200px;
  width: 400px;
  border: 1px solid gray;
}



</style>