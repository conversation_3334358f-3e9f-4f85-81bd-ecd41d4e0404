<template>

<div class="content">


    <div class="navbar">
		<!-- a href="javascript: alert('Nach hause telefonieren');" target="_self" class="logo"><img src="/images/logo.png"></a -->

        <!-- div role="group" aria-label="Basic example" class="btn-group">
            <button type="button" @click="toggleCoverList()" :class="{ active: showCoverList }" class="btn btn-sm base-button btn-secondary btn-icon btn-fab">
                <span class="btn-inner--icon">
                    <i class="ni ni-image"></i>
                </span>
            </button>
        </div -->


		<ul class="nav">
			<li></li>
		</ul>	

        <b-col class="md4"></b-col>
        <b-col class="md4 text-center">
            <div v-if="!decisionBar" class="decisionBar">
                Please select a proposal
            </div>
        </b-col>
        <b-col class="md4"></b-col>

        <div v-if="decisionBar" class="decisionBar">
            <base-button icon class="btn-pink" @click="layoutAccepted()" v-if="!changeRequest">
                <span class="btn-inner--icon text-white"><i class="ni ni-check-bold"></i></span>
                <span class="btn-inner--text text-white">Accept proposal</span>
            </base-button>

            <base-button icon class="btn-black" @click="layoutRejected()" v-if="!changeRequest">
                <span class="btn-inner--icon text-white"><i class="ni ni-fat-remove"></i></span>
                <span class="btn-inner--text text-white">Reject proposal</span>
            </base-button>

            <base-button icon type="info" @click="layoutChangeRequested()">
                <span class="btn-inner--icon text-white"><i class="ni ni-settings"></i></span>
                <span class="btn-inner--text text-white">Request change</span>
            </base-button>

            <base-button icon class="btn-success" @click="downloadPDF()">
                <span class="btn-inner--icon text-white"><i class="ni ni-cloud-download-95"></i></span>
                <span class="btn-inner--text text-white">Download PDF</span>
            </base-button>
        </div>
       
	</div>

    
    <b-row class="justify-content-md-center topspace">

        <photo-book-cover-list :fetchCovers="fetchCovers" :photoBookId="this.token" v-if="showCoverList" @coverSelected="coverSelected"></photo-book-cover-list>

    </b-row>

    <b-row class="justify-content-md-center bottomspace">

        <photo-book-cover-preview-card :photoBookId="this.token" :imageId="this.image" :showComments="this.showComments" v-if="cload"></photo-book-cover-preview-card>

    </b-row>	



    </div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'

import PhotoBookCoverList from '../cscomponents/PhotoBookCoverList';
import PhotoBookCoverPreviewCard from '../cscomponents/PhotoBookCoverPreviewCard';
// import PhotoBookDesignerImageList from '../cscomponents/designer/PhotoBookDesignerImageList';

import AppStyle from '@/plugins/appStyle';
import VueFabric from 'vuejs-fabric';

import swal from 'sweetalert2';
import 'sweetalert2/dist/sweetalert2.css';

export default {
    name: 'photo-book-cover-preview',
    mixins: [AppStyle.Mixin],
    components: {
        // PhotoBookDesignerImageList,
        PhotoBookCoverList,
        PhotoBookCoverPreviewCard,
        VueFabric,
    },
    data() {
        return {
            urls: [],
            loading: true,
            showCoverList: true,
            width: 600,
            height: 400,
            coverSelect: false,
            decisionBar: false,
            changeRequest: false,
            showComments: false,

            image: false,

            /// tmp
            cload: false,
            fetchCovers: 1,
        }
    },
    props: {
        token: Number,
    },
    methods: {

        coverSelected(cover_id, state){
            console.log(cover_id);
            console.log("COVER Selectede in OPreviewe");

           // this.image = cover_id;
            this.image = cover_id;
           // this.image.state = state;

            // set co ver as fabric bg
            this.coverSelect = true;
            
            console.log("reported cover state === "+state);
            if(state == "rejected"){
                this.decisionBar = false;
            }
            else{
                this.decisionBar = true;
            }

            this.cload = true;
            this.changeRequest = false;
        },
        retrievePhotoBook() {
            this.loading = true;
            this.$store.dispatch('fetchPhotoBook', this.token)
            .then(() => {
                console.log(this.$store.getters.currentPhotoBook);
            })
            .catch((error) => {
                console.error(error);
                this.loading = true;
            }).finally(() => {
                    this.loading = false;
                });
        },


        toggleCoverList() {
            if (this.showCoverList) {
                this.showCoverList = false;
            }
            else {
                this.showCoverList = true;
            }
        },

        toggleComments() {
            if (this.showComments) {
                this.showComments = false;
            }
            else {
                this.showComments = true;
            }
        },

        mayFinishPopup(){
            // are you done ???
        },

        layoutAccepted(){
            swal.fire({
                title: 'Layout Proposal accepted',
                text: 'Thank you for accepting the layout proposal.',
                timer: 5000,
                buttonsStyling: false,
                confirmButtonClass: 'btn btn-pink',
                icon: 'success'
            }).then((result) => {
                this.decisionBar = false;
                this.$api.acceptCoverLayout(this.token, this.image).then((response) => {
                    console.log("response for accept");
                    console.log(response);
                    this.image.state = "accepted";

                }).catch((e) => {
                    console.log(e);
                }).finally(() => {
                    this.loading = false;
                    this.$store.dispatch('photoBookNextStep');
                });
            });
        },
        layoutRejected(){
            swal.fire({
                title: 'Are you sure?',
                text: "If you reject all 3 layout asuggestions we ask you to create your cover layout on your own.",
                timer: 5000,
                showCancelButton: true,
                buttonsStyling: false,
                confirmButtonClass: 'btn btn-pink',
                confirmButtonText: 'Yes, reject!',
                cancelButtonClass: 'btn btn-dark',
                icon: 'info'
            }).then((result) => {
          if (result.value) {
              swal.fire({
                    title: 'Layout Proposal rejected',
                    text: 'Thank you for rejecting the layout proposal.',
                    type: 'info',
                    buttonsStyling: false,
                    confirmButtonClass: 'btn btn-info'
              }).then((result) => {
                this.decisionBar = false;

                this.loading = true;
                this.$api.rejectCoverLayout(this.token, this.image).then((response) => {
                    console.log("response for reject");
                    console.log(response);
                    console.log("Setting photo_book trigger_cover_rejected to true");
                    this.fetchCovers = Math.floor(Math.random() * 30);
                    this.currentPhotoBook.trigger_cover_rejected = true;
                    console.log(this.$store.getters.currentPhotoBook.trigger_cover_rejected);
                 //   this.image.state = "rejected";

                }).catch((e) => {
                    console.log(e);
                }).finally(() => {
                    this.loading = false;
                });


            });
          }
          else{
            this.decisionBar = true;
          }
        })
        },
        layoutChangeRequested(){
            swal.fire({
                title: 'Layout Proposal change requested',
                text: 'Please describe the changes you want in the comments section below.',
                timer: 5000,
                buttonsStyling: false,
                confirmButtonClass: 'btn btn-info',
                icon: 'success'
            }).then((result) => {
               // this.decisionBar = false;
                this.showComments = true;
                this.changeRequest = true;
            });
        },

        downloadPDF() {
            // Implement your PDF download logic here
            console.log("Downloading PDF...");
        },
        
    },
    computed: {
        ...mapGetters({
            currentAccount: 'currentAccount',
            currentPhotoBook: 'currentPhotoBook',
        }),
        ...mapActions({
            fetchPhotoBook: 'fetchPhotoBook',
        }),
    },
    mounted() {
        this.retrievePhotoBook();
    },
    created() {
    },
}
</script>

<style scoped>

* {
    margin: 0;
    /*
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    */
    vertical-align: baseline;
    box-sizing: border-box;
    color: inherit;
}

html,body {
    height: 100vh;
	width: 100vw; 
	/* 
	background: #f8f9fa;
	color: #344767;
	
	font-size: 16px;
	line-height: 1.4em;
	font-family: "Poppins", sans-serif;
	font-weight: 300;
    */
	position: relative;
}

/* Navbar */
.navbar{
	position: fixed;
	height: 80px;
	background: #FFF;
	/* 
    border-radius: 1rem; 
    */
	left: 0px;
	right: 0px;
	top: 0px;
	/*
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
	/* 
    padding: 2rem;
	*/
    display: flex;
	flex-direction: row;
	justify-content: space-between; 
	/*
    align-items: center;
	*/
    z-index: 21;
}
.navbar a.logo{
	width: 160px; 
	img{
		max-width: 100%;
		height: auto;
	}
}
.navbar ul.nav{
	list-style-type: none;
	display: flex;
	flex-direction: row;
	gap: 15px;
	align-items: center;
	justify-content: flex-end;
	margin: 0;
	padding: 0;
}
.navbar ul.nav li{
	display: inline-flex;
	margin: 0;
	padding: 0;
}
.navbar img{
	max-width: 100%;
	height: auto;
}
/* Struktur */
.dasgrosseganze{
	display: flex;
	flex-direction: row;
	justify-content: space-beetwen; 
	height: 100vh;
	width: 100vw;
	padding-top: 100px;
}
.sidebar{
	position: absolute;
	left: 0px;
	bottom: 0px;
	top: 70px;
	width: 170px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 1rem;
	z-index: 20;
	background: #FFF;
	
	/*border-radius: 1rem;*/
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	
}
.sidebar img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}

.sidebar_img{
	position: absolute;
	left: 150px;
	bottom: 0px;;
	top: 70px;
	width: 120px;
	overflow-x: hidden;
	overflow-y: auto;
	/*
    padding: 1rem;
	*/
    z-index: 20;
	background: #FFF;
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.sidebar_img img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}


.toolbar{
	position: absolute;
	right: 0px;
	bottom: 0px;
	top: 70px;
	width: 200px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 1rem;
	z-index: 20;
	background: #FFF;
	
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.toolbar img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}

.topspace {
    position: relative;
	top: 20px !important;
}

.bottomspace{
    position: relative;
    top: 20px;
    padding-top: 80px;
}

.docviewwrapper{
	position: fixed;
	/*
    left: 304px;
	right: 150px;
	*/
    left: 270px;
	right: 200px;
    bottom: 1rem;
	top: 80px;
	/*
	right: 1rem;
	*/
}

.pt100 {
    padding-top:80px;
}
@media screen and (min-width: 992px){
	.docview{
		display: flex;
		flex-direction: row;
		gap: 0px;
		justify-content: center;
		align-content: center;
		padding: 30px;
		height: 100%;
	}

	.page_left{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: right;
		justify-content: right;
		padding: 0px;
	}

	.page_right{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: left;
		justify-content: left;
		padding: 0px;
	}

	.docview .page_left img{
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
		display: block;
		max-width: 100%;
		max-height: 100%;
	}

	.docview .page_right img{
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
		display: block;
		max-width: 100%;
		max-height: 100%;
	}
	.docview a{
		display: flex;
		height: 100%;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}
}
@media screen and (max-width: 992px){
	.docviewwrapper{
		overflow-x: hidden;
		overflow-y: auto;
		bottom: 0;
		top: 90px;
	}
	.docview {
		display: block;
		padding: 0 20px;
	}
	.docview a{
		display: block;
		padding: 20px;
	}
	.docview{
		flex-wrap: wrap;
	}
	.docview img{
		display: block;
		max-width: 100%;
		height: auto;
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
	}
} 



.spine{
    background-color: pink;
    width: 100px;
}
.imglist {
    /*
    background-color: red;
    */
    overflow-y: scroll;
    max-height: 80vh;
    padding-bottom: 0px;
    margin-left: 0px;
}

.maxih {
    background-color: red;
    max-height: 80vh;
    min-height: 80vh;
    padding-bottom: 0px;
    margin-left: 0px;
}

.photo_book_image_list_wrapper {
    padding-top: 0.5rem;
    padding-right: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.landscape {
    width: 148mm;
    height: 105mm;

    width: 222mm;
    height: 157mm;
}

.portrait {
    height: 297mm;
    width: 210mm;

    width: 157mm;
    height: 222mm;


}

</style>