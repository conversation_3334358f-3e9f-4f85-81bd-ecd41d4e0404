
<template>


    <div>
        <div class="loader" v-if="loading"></div>
        <base-header class="pb-6 bg-white" v-if="!$route.meta.hideHeader">
            <b-row align-v="center" class="py-4">
                <b-col cols="7" lg="6">
                    <h6 :class="AppStyle.BREADCRUMB_H_STYLE" class="h2 d-inline-block mb-0">Your profile</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <route-bread-crumb></route-bread-crumb>
                    </nav>
                </b-col>
            </b-row>
        </base-header>
        <b-container fluid class="mt--6 bg-white">



            <div class="row justify-content-center">



                <b-col md="1">

                        <p><a href="#" @click="toggelMainAccountData">General</a></p>
                        <p><a href="#" @click="toggelAccountAddressData">Address</a></p>
                        <p><a href="#" @click="toggelProfileImage">Avatar</a></p>
                        <p><a href="#" @click="togglePassword">Password</a></p>
 


                </b-col>

                <div class="col-lg-5 card-wrapper metadata">

                    
                    <!-- div class="mb-4">
                        <b-button @click="toggelMainAccountData">Main account data</b-button>
                        <b-button @click="toggelAccountAddressData">Account address data</b-button>
                        <b-button @click="toggelProfileImage">Profile image</b-button>
                        <b-button @click="togglePassword">Change Password</b-button>
                    </div -->


                    <card v-if="passwordData">
                        <!-- Card header -->

                        <div class="row pb-3">
                                <h1>Change password</h1>
                            </div>
                        <div class="card-body">
                            <!-- Card body -->
                            <validation-observer v-slot="{ handleSubmit }" ref="formValidator">
                                <form class="needs-validation" @submit.prevent="handleSubmit(firstFormSubmit)">
                                    <!-- Input groups with icon -->
                                    <b-row>

                                        <b-col md="12" v-if="!passwords_match" style="color: red;">
                                            <p>Passwords do not match!</p>
                                        </b-col>


                                        <b-col md="6">
                                            <base-input label="Password" type="password" prepend-icon="fas fa-lock"
                                                placeholder="Password" name="Password" rules="required"
                                                v-model="currentAccount.password"></base-input>

                                        </b-col>
                                        <b-col md="6">
                                            <base-input label="Password confirmation" type="password"
                                                prepend-icon="fas fa-lock" placeholder="Password confirmation"
                                                name="Password confirmation" rules="required"
                                                v-model="currentAccount.password_confirmation"></base-input>

                                        </b-col>

                                    </b-row>

                                    <b-row>
                                        <b-col md="12">
                                            <base-button type="primary" class="btn btn-dark" @click="updateProfile">Save
                                                profile</base-button>
                                        </b-col>
                                    </b-row>

                                </form>
                            </validation-observer>
                        </div>
                    </card>

                    <card v-if="mainAccountData">
                        <!-- Card header -->


                        <div class="card-body">

                            <div class="row pb-3">
                                <h1>Account data</h1>
                            </div>


                            <!-- Card body -->
                            <validation-observer v-slot="{ handleSubmit }" ref="formValidator">
                                <form class="needs-validation" @submit.prevent="handleSubmit(firstFormSubmit)">
                                    <!-- Input groups with icon -->
                                    <b-row>

                                        <b-col md="6">
                                            <base-input label="Firstname" prepend-icon="fas fa-user"
                                                placeholder="Firstname" name="Firstname" rules="required"
                                                v-model="currentAccount.firstname"></base-input>

                                        </b-col>
                                        <b-col md="6">
                                            <base-input label="Lastname" prepend-icon="fas fa-user"
                                                placeholder="Lastname" name="Lastname" rules="required"
                                                v-model="currentAccount.lastname"></base-input>

                                        </b-col>

                                        <b-col md="12">
                                            <base-input label="Email" prepend-icon="fas fa-envelope"
                                                placeholder="Email address" name="email"
                                                rules="required|email"
                                                v-model="currentAccount.email"></base-input>

                                        </b-col>

                                        <b-col md="12">
                                            <base-input label="instagram_account_name" prepend-icon="fas fa-user"
                                                placeholder="Instagram name" name="instagram_account_name"
                                                rules="required"
                                                v-model="currentAccount.instagram_account_name"></base-input>

                                        </b-col>

                                    </b-row>

                                    <b-row>
                                        <b-col md="12">
                                            <base-button type="primary" class="btn btn-dark" @click="updateProfile">Save
                                                profile</base-button>
                                        </b-col>
                                    </b-row>

                                </form>
                            </validation-observer>
                        </div>
                    </card>

                    <card v-if="accountAddressData">
                        <!-- Card header -->


                        <div class="card-body">

                            <div class="row pb-3">
                                <h1>Your shipping address</h1>
                            </div>



                            <!-- Card body -->
                            <validation-observer v-slot="{ handleSubmit }" ref="formValidator">
                                <form class="needs-validation" @submit.prevent="handleSubmit(firstFormSubmit)">
                                    <!-- Input groups with icon -->
                                    <b-row>

                                        <b-col md="6">
                                            <base-input label="Firstname" prepend-icon="fas fa-user"
                                                placeholder="Firstname" name="Firstname" rules="required"
                                                v-model="currentAccount.firstname"></base-input>

                                        </b-col>
                                        <b-col md="6">
                                            <base-input label="Lastname" prepend-icon="fas fa-user"
                                                placeholder="Lastname" name="Lastname" rules="required"
                                                v-model="currentAccount.lastname"></base-input>

                                        </b-col>

                                        <b-col md="12">
                                            <base-input label="Adress line 1" prepend-icon="fas fa-user"
                                                placeholder="Adress line 1" name="address_line_1"
                                                rules="required"
                                                v-model="currentAccount.address.address_line_1"></base-input>

                                        </b-col>

                                        <b-col md="12">
                                            <base-input label="Adress line 2" prepend-icon="fas fa-user"
                                                placeholder="Adress line 2" name="address_line_2"
                                                rules="required"
                                                v-model="currentAccount.address.address_line_2"></base-input>

                                        </b-col>

                                        <b-col md="12">
                                            <base-input label="Adress line 3" prepend-icon="fas fa-map-marker-alt"
                                                placeholder="Adress line 3" name="address_line_3"
                                                rules="required"
                                                v-model="currentAccount.address.address_line_3"></base-input>

                                        </b-col>


                                        <b-col md="12">
                                            <label>Country of Origin</label>
                                            <base-input name="Country of Origin" rules="required">
                                                <el-select v-model="currentAccount.address.country_name" filterable placeholder="Country"
                                                    rules="required">
                                                    <el-option v-for="option in countries" :key="option.label" :label="option.label"
                                                        :value="option.value">
                                                    </el-option>
                                                </el-select>
                                            </base-input>

                                        </b-col>


                                        <b-col md="6">
                                            <base-input label="Phone" prepend-icon="fas fa-phone"
                                                placeholder="Phone" name="phone"
                                                rules="required"
                                                v-model="currentAccount.address.phone"></base-input>

                                        </b-col>

                                        <b-col md="6">
                                            <base-input label="Email" prepend-icon="fas fa-envelope"
                                                placeholder="email" name="email"
                                                rules="required"
                                                v-model="currentAccount.address.email"></base-input>

                                        </b-col>

                                    </b-row>

                                    <b-row>
                                        <b-col md="12">
                                            <base-button type="primary" class="btn btn-dark" @click="updateProfile">Save
                                                shipping address</base-button>
                                        </b-col>
                                    </b-row>

                                </form>
                            </validation-observer>
                        </div>
                    </card>

                    <card v-if="profileImage">
                        <!-- Card header -->


                        <div class="card-body">

                            <div class="row pb-3">
                                <h1>Your profile picture</h1>
                            </div>


                            <!-- Card body -->
                            <validation-observer v-slot="{ handleSubmit }" ref="formValidator">
                                <form class="needs-validation" @submit.prevent="handleSubmit(firstFormSubmit)">
                                    <!-- Input groups with icon -->
                                    <b-row>

                                        <b-col md="6">
                                            <base-input label="Profile picture" type="file" prepend-icon="fas fa-lock"
                                                placeholder="Profile picture" name="Profile picture" rules="required"
                                                v-model="currentAccount.profile_image"></base-input>

                                        </b-col>

                                    </b-row>

                                    <b-row>
                                        <b-col md="12">
                                            <base-button type="primary" class="btn btn-dark" @click="updateProfile">Save
                                                profile</base-button>
                                        </b-col>
                                    </b-row>

                                </form>
                            </validation-observer>
                        </div>
                    </card>
                </div>
            </div>


        </b-container>
    </div>
</template>
<script>

import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import Badge from '@/components/Badge';
import { Tabs, TabPane } from '@/components';
import { Tooltip } from 'element-ui'
import TagsInput from '@/components/Inputs/TagsInput'
import { Select, Option } from 'element-ui'

import Comments from '../cscomponents/Comments';

import AppStyle from '@/plugins/appStyle';

export default {
    name: "snap-profile",
    mixins: [AppStyle.Mixin],
    components: {
        TagsInput,
        [Tooltip.name]: Tooltip,
        [Select.name]: Select,
        [Option.name]: Option,
        RouteBreadCrumb,
        Badge,
        Tabs,
        TabPane,
        Comments,
    },
    data() {
        return {
            loading: true,
            photoBook: null,
            actiTab: 'Dokumente',
            accountAddressData: false,
            profileImage: false,
            mainAccountData: false,
            passwordData: false,
            countries: [],
        }
    },
    props: {
        token: String,
    },
    methods: {
        fetchCurrentAccount() {
            this.loading = true;
            this.$api.getAccount()
                .then((response) => {
                    console.log("Fetched account data:", response.data);
                    this.$store.commit('setAccount', response.data);
                })
                .catch((e) => {
                    console.log("Error fetching account:", e);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        fetchCountries() {
            this.loading = true;
            this.$api.getCountries()
                .then((response) => {
                    const countries = response.data;
                    this.countries = countries.countries;
                    console.log(this.countries);
                })
                .catch((e) => {
                    console.log(e);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        togglePassword() {
            if (this.passwordData) {
                //   this.passwordData = false;
            } else {
                this.passwordData = true;

                this.mainAccountData = false;
                this.accountAddressData = false;
                this.profileImage = false;
            }
        },

        toggelMainAccountData() {
            if (this.mainAccountData) {
                //  this.mainAccountData = false;
            } else {
                this.mainAccountData = true;
                this.passwordData = false;
                this.accountAddressData = false;
                this.profileImage = false;
            }


        },
        toggelAccountAddressData() {
            if (this.accountAddressData) {
                // this.accountAddressData = false;
            } else {
                this.accountAddressData = true;

                this.passwordData = false;
                this.mainAccountData = false;
                this.profileImage = false;
            }
        },
        toggelProfileImage() {
            if (this.profileImage) {
                //  this.profileImage = false;
            } else {
                this.profileImage = true;

                this.passwordData = false;
                this.mainAccountData = false;
                this.accountAddressData = false;
            }
        },
        updateProfiles() {
            this.passwordData = false;
            this.mainAccountData = false;
            this.accountAddressData = false;
        },
        updateProfile() {
            this.loading = true;
            this.$api.updateAccount(this.currentAccount, this.currentAccount)
                .then((response) => {
                    console.log("account");
                    console.log(response.data);
                    console.log("account end");
                    //this.currentAccount = response.data.account;
                    this.$store.commit('setAccount', response.data, response.data);
                })
                .catch((e) => {
                    console.log(e);
                }).finally(() => {
                    this.loading = false;
                });
        },
    },
    computed: {
        ...mapGetters({
            currentAccount: 'currentAccount',
        }),
    },
    mounted() {
        this.loading = false;
        this.toggelMainAccountData();
        this.currentAccount.address = [];
        this.fetchCountries();
    },
    created() {
    },
}
</script>
