<template>
    <div>
        
        

    <div class="content">
        <s09 v-if="this.next_step == 9" @allowProceed="allowProceed" @denyProceed="denyProceed"></s09>
        <div class="loader" v-if="loading"></div>
        <base-header class="pb-6 bg-white" v-if="!$route.meta.hideHeader">
            <b-row align-v="center" class="py-4">
                <b-col cols="7" lg="6">
                    <h6 :class="AppStyle.BREADCRUMB_H_STYLE" class="h2 d-inline-block mb-0">Art Book {{ currentPhotoBook.id
                    }}</h6>
                    <nav aria-label="breadcrumb" class="d-none d-md-inline-block ml-md-4">
                        <route-bread-crumb></route-bread-crumb>
                    </nav>
                </b-col>
            </b-row>
        </base-header>
        <b-container fluid class="mt--6">

            <div>

                <wizard-header></wizard-header>

                <s01 v-if="this.next_step == 1" @allowProceed="allowProceed" @denyProceed="denyProceed"></s01>

                <s02 v-if="this.next_step == 2" @allowProceed="allowProceed" @denyProceed="denyProceed"></s02>

                <s03 v-if="this.next_step == 3" @allowProceed="allowProceed" @denyProceed="denyProceed"></s03>

                <s04 v-if="this.next_step == 4" @allowProceed="allowProceed" @denyProceed="denyProceed" @nextStep="nextStep" @prevStep="prevStep"></s04>

                <s05 v-if="this.next_step == 5" @allowProceed="allowProceed" @denyProceed="denyProceed"></s05>

                <s06 v-if="this.next_step == 6" @allowProceed="allowProceed" @denyProceed="denyProceed"></s06>

                <s07 v-if="this.next_step == 7" @allowProceed="allowProceed" @denyProceed="denyProceed"></s07>

                <s08 v-if="this.next_step == 8" @allowProceed="allowProceed" @denyProceed="denyProceed"></s08>

                <!-- s09 v-if="this.next_step == 9" @allowProceed="allowProceed" @denyProceed="denyProceed"></s09 -->

                <s10 v-if="this.next_step == 10" @allowProceed="allowProceed" @denyProceed="denyProceed"></s10>
                
                <s11 v-if="this.next_step == 11" @allowProceed="allowProceed" @denyProceed="denyProceed"></s11>

                <s12 v-if="this.next_step == 12" @allowProceed="allowProceed" @denyProceed="denyProceed"></s12>
            </div>

            <div class="btn-wrapper text-center" v-if="this.next_step != 10 && this.next_step != 6  && this.next_step != 7 && this.next_step != 9 && this.next_step != 12">

                <base-button type="primary" v-if="this.next_step > 1" class="btn btn-dark" @click="prevStep"  :disabled="!allDataValidated">
                    <span class="btn-inner--text">Back</span>
                </base-button>

                <base-button type="pink" class="btn btn-pink" @click="nextStep" :disabled="!allDataValidated">
                    <span class="btn-inner--text">Proceed</span>
                </base-button>

            </div>

        </b-container>
    </div></div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'
import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb'
import Badge from '@/components/Badge';


import moment from 'moment'
import AppStyle from '@/plugins/appStyle';
import WizardHeader from '@/views/Pages/PhotoBookWizard/WizardHeader';

import note from '@/views/Pages/PhotoBookWizard/Steps/note';

import s01 from '@/views/Pages/PhotoBookWizard/Steps/s01';
import s02 from '@/views/Pages/PhotoBookWizard/Steps/s02';
import s03 from '@/views/Pages/PhotoBookWizard/Steps/s03';
import s04 from '@/views/Pages/PhotoBookWizard/Steps/s04';
import s05 from '@/views/Pages/PhotoBookWizard/Steps/s05';
import s06 from '@/views/Pages/PhotoBookWizard/Steps/s06';
import s07 from '@/views/Pages/PhotoBookWizard/Steps/s07';
import s08 from '@/views/Pages/PhotoBookWizard/Steps/s08';
import s09 from '@/views/Pages/PhotoBookWizard/Steps/s09';
import s10 from '@/views/Pages/PhotoBookWizard/Steps/s10';
import s11 from '@/views/Pages/PhotoBookWizard/Steps/s11';
import s12 from '@/views/Pages/PhotoBookWizard/Steps/s12';

export default {
    mixins: [AppStyle.Mixin],
    components: {
        RouteBreadCrumb,
        note,
        s01,
        s02,
        s03,
        s04,
        s05,
        s06,
        s07,
        s08,
        s09,
        s10,    
        s11,
        s12,
        WizardHeader,
    },
    data() {
        return {
            loading: false,
            allDataValidated: false,
        }
    },
    props: {
        token: String,
    },
    methods: {

        toggleOptions() {
            if (this.showOptions) {
                this.showOptions = false;
            }
            else {
                this.showOptions = true;
            }
        },
        pageClass() {
            return this.currentPhotoBook.orientation;
        },
        allowProceed(){
            console.log("allowing proceed");
            this.allDataValidated = true;
        },
        denyProceed(){
            console.log("denying proceed");
            this.allDataValidated = false;
        },
        /*
        allDataValidated: function () {
            return true;
        },*/
        nextStep() {
            // update title
            // reload title && show next step
            this.loading = true;
            console.log("Perform next step");
            this.$store.dispatch('photoBookNextStep');
            this.loading = false;
        },
        prevStep() {
            // reload title
            // show prev step            
            this.loading = true;
            console.log("Perform prev step");
            this.$store.dispatch('photoBookPrevStep');
            this.loading = false;
        },
    },
    /*
    channels: {
        ClaimsChannel: {
            connected() {
                //console.log('I am connected to claims');
            },
            rejected() {
                //console.log('I am REJECTED.');
            },
            received(data) {
                //        console.log("RECEIVED!");
                //        console.log(data);
                // switch case if documents_generated
                // switch case if documents_signed
                // do something on receive... check if docs generated
                if (data.action == "signed") {
                    this.claim.attachments = data.payload;
                    this.claim.documents_signed = true;
                }

                if (data.action == "generated") {
                    this.claim.attachments = data.payload;
                    this.claim.documents_generated = true;
                }
            },
            disconnected() {
                console.log('I am disconnected.');
            },
        }
    },
    */
    computed: {
        ...mapGetters({
            currentAccount: 'currentAccount',
            currentSettings: 'currentSettings',
            currentPhotoBook: 'currentPhotoBook',
            currentPhotoBookStep: 'currentPhotoBookStep',
        }),
        ...mapState(
            {
                photo_book: (state) => state.photo_book,
            }),
        ...mapActions({
            fetchPhotoBook: 'fetchPhotoBook',
            photoBookNextStep: 'photoBookNextStep',
            photoBookPrevStep: 'photoBookPrevStep',
        }),
        midWidth() {
            let w = 12;
            if (this.showPageList) {
                w -= 1;
            }
            if (this.showImageList) {
                w -= 2;
            }
            if (this.showOptions) {
                w -= 2;
            }
            console.log(w);
            return w;
        },
        next_step: (state) => state.photo_book.photo_book.next_step,
        mayProceed: function () {
            return this.allDataValidated;
        },
        mayGoBack: function () {
            return this.allDataValidated;
        },
    },
    watch: {
        next_step(newValue, oldValue) {
            console.log('queryParameter next_step changed from: '+oldValue+' -> '+newValue);

            // redirect if needed -> dashboard
        },
        currentPhotoBook: {
            handler:function (newItem) {
                if(this.next_step == 0){
                    this.nextStep();
                }
                if(this.next_step == 1){
                    this.nextStep();
                }
            },
            deep: true
        },
    },
    mounted() {
        console.log(this.token);
        this.$store.dispatch('fetchPhotoBook', this.token);
        //console.log(this.currentPhotoBookStep);
        //console.log(this.step);
        /*
        console.log("MOUNTED");
        console.log(this.$store.state.photo_book.photo_book);
        console.log(this.$store.state.photo_book.photo_book.next_step);
        console.log("MOUNTED END");
        */
    },
    created() {
    },
}
</script>