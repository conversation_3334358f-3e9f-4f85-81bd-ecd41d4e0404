<template>

<div class="content">


    <div class="navbar">
		<!-- a href="javascript: alert('Nach hause telefonieren');" target="_self" class="logo"><img src="/images/logo.png"></a -->

        <div role="group" aria-label="Basic example" class="btn-group">
            <button type="button" @click="togglePageList()" :class="{ active: showPageList }" class="btn btn-sm base-button btn-secondary btn-icon btn-fab">
                <span class="btn-inner--icon">
                    <i class="ni ni-book-bookmark"></i>
                </span>
                <!-- span class="btn-inner--text">Pages</span -->
            </button>
            <button type="button" @click="toggleImageList()" :class="{ active: showImageList }" class="btn btn-sm base-button btn-secondary btn-icon btn-fab">
                <span class="btn-inner--icon">
                    <i class="ni ni-image"></i>
                </span>
                <!-- span class="btn-inner--text">Images</span -->
            </button>
        </div>

        <div>
    <button @click="addTextElement">Add Text</button>
    <button >-</button>
    <button >+</button>
  </div>

		<ul class="nav">
			<!-- li><b-button @click="toggleImageList()">Images</b-button></li>
			<li><b-button @click="togglePageList()">Pages</b-button></li -->
			<li><b-button @click="toggleOptions()">Options</b-button></li>
		</ul>	

	</div>

    
    <div class="dasgrosseganze">

        <photo-book-designer-image-list :photoBookId="this.token" v-if="showImageList" @imageselected="imageSelected"></photo-book-designer-image-list>
        <photo-book-designer-page-list :photoBookId="this.token" v-if="showPageList"></photo-book-designer-page-list>

        <div class="docviewwrapper"> 
			<div class="docview" id="docview" ref="docview">


                <photo-book-designer-page ref="pageLeft" :klass="'page_s page_left'" :photoBookId="this.token" token="ce461a41-fd89-4e50-9391-ccdfa2640050"></photo-book-designer-page>
                <photo-book-designer-page ref="pageRight" :klass="'page_s page_right'" :photoBookId="this.token" token="ce461a41-fd89-4e50-9391-ccdfa2640050"></photo-book-designer-page>

			</div>	
		</div>	

        <div class="toolbar" v-if="showOptions">
			<div class="thumbwrapper">
                OPTIONS
				<a href="#" class="thumb"><img src="/images/thumb01.jpg"></a>
			</div>

            <div class="fontoptions" ref="fontoptions" v-if="showFontOptions">
                <base-input label="Basic select">
                        <select class="form-control" id="exampleFormControlSelect1">
                        <option>1</option>
                        <option>2</option>
                        <option>3</option>
                        <option>4</option>
                        <option>5</option>
                        </select>
                </base-input>
                <base-input type="color" value="#5e72e4" id="example-color-input"/>
            </div>

            <div class="imageoptions" ref="imageoptions" v-if="showImageOptions">
                Imageoptions
            </div>

            <div class="pagelayers" ref="pagelayers" v-if="showLayerOptions">
                Layeroptions
            </div>

		</div>

    </div>

    </div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex'
import PhotoBookImageList from '../cscomponents/PhotoBookImageList';
import PhotoBookImageUploader from '../cscomponents/PhotoBookImageUploader';

// import PhotoBookDesignerPage from '../cscomponents/designer/PhotoBookDesignerPage';
// import PhotoBookDesignerPageList from '../cscomponents/designer/PhotoBookDesignerPageList';
// import PhotoBookDesignerImageList from '../cscomponents/designer/PhotoBookDesignerImageList';

import PhotoBookPage from '../cscomponents/PhotoBookPage';
import PhotoBookPageList from '../cscomponents/PhotoBookPageList';

import moment from 'moment'
import AppStyle from '@/plugins/appStyle';

import DropzoneFileUpload from '@/components/Inputs/DropzoneFileUpload'


export default {
    mixins: [AppStyle.Mixin],
    components: {
        // PhotoBookDesignerImageList,
        PhotoBookDesignerPageList,
        PhotoBookImageUploader,
        PhotoBookPage,
        PhotoBookDesignerPage,
        DropzoneFileUpload,
    },
    data() {
        return {
            loading: true,
            photoBook: null,
            photoBookPages: [],
            showPageList: false,
            showOptions: false,
            showImageList: false,

            showFontOptions: true,
            showImageOptions: true,
            showLayerOptions: true,

            vueCanvas: '',
            rectWidth: 0,

            inputs: {
                fileSingle: '',
            },
            rows: 100,
         perPage: 1,
         currentPage: 2,
        }
    },
    props: {
        token: String,
        tab: String,
    },
    methods: {
        imageSelected(img){
            console.log("SELECTED iamge"+img);
            console.log("SELECTED iamge"+img.id);
            this.$refs.pageLeft.addImage(img);
        },
        retrievePhotoBook() {
            this.loading = true;
            this.$store.dispatch('fetchPhotoBook', this.token)
            .then(() => {
                console.log(this.$store.getters.currentPhotoBook);
            })
            .catch((error) => {
                console.error(error);
                this.loading = true;
            }).finally(() => {
                    this.loading = false;
                });

                console.log("The current book");
                console.log(this.currentPhotoBook);
                console.log(this.$store.getters.currentPhotoBook);
                console.log("The current book end");
        },

        toggleImageList() {
            if (this.showImageList) {
                this.showImageList = false;
            }
            else {
                this.showImageList = true;
            }
        },

        togglePageList() {
            if (this.showPageList) {
                this.showPageList = false;
            }
            else {
                this.showPageList = true;
            }
        },

        toggleOptions() {
            if (this.showOptions) {
                this.showOptions = false;
            }
            else {
                this.showOptions = true;
            }
        },

        addedfile(){
            console.log("ADDEDFILE");
            console.log(this.inputs);
            console.log(this.inputs.fileSingle[0]);
            console.log(this.inputs.fileSingle[0].dataURL);
        },
        
        handleOrientationSet(){

        },

        pageClass(){
            return this.currentPhotoBook.orientation;
        },


        addPage(){
        },

        addTextElement(){
            this.$refs.pageLeft.addText();

        },

        /**
         * Conserve aspect ratio of the original region. Useful when shrinking/enlarging
         * images to fit into a certain area.
         *
         * @param {Number} srcWidth width of source image
         * @param {Number} srcHeight height of source image
         * @param {Number} maxWidth maximum available width
         * @param {Number} maxHeight maximum available height
         * @return {Object} { width, height }
         */
         calculateAspectRatioFit(srcWidth, srcHeight, maxWidth, maxHeight) {

        var ratio = Math.min(maxWidth / srcWidth, maxHeight / srcHeight);

        return { width: srcWidth*ratio, height: srcHeight*ratio };
        },
        
    },
    computed: {
        ...mapGetters({
            currentAccount: 'currentAccount',
            currentSettings: 'currentSettings',
            currentPhotoBook: 'currentPhotoBook',
        }),
        ...mapActions({
            fetchPhotoBook: 'fetchPhotoBook',
        }),
    },
    mounted() {
        this.retrievePhotoBook();
        

    },
    created() {
    },
}
</script>
<style scoped>

* {
    margin: 0;
    /*
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    */
    vertical-align: baseline;
    box-sizing: border-box;
    color: inherit;
}

html,body {
    height: 100vh;
	width: 100vw; 
	/* 
	background: #f8f9fa;
	color: #344767;
	
	font-size: 16px;
	line-height: 1.4em;
	font-family: "Poppins", sans-serif;
	font-weight: 300;
    */
	position: relative;
}

a, a:link, a:visited{
	text-decoration: none;
	color: #344767;
}


/* Navbar */
.navbar{
	position: fixed;
	height: 70px;
	background: #FFF;
	/* 
    border-radius: 1rem; 
    */
	left: 0px;
	right: 0px;
	top: 0px;
	/*
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
	/* 
    padding: 2rem;
	*/
    display: flex;
	flex-direction: row;
	justify-content: space-between; 
	/*
    align-items: center;
	*/
    z-index: 21;
}
.navbar a.logo{
	width: 160px; 
	img{
		max-width: 100%;
		height: auto;
	}
}
.navbar ul.nav{
	list-style-type: none;
	display: flex;
	flex-direction: row;
	gap: 15px;
	align-items: center;
	justify-content: flex-end;
	margin: 0;
	padding: 0;
}
.navbar ul.nav li{
	display: inline-flex;
	margin: 0;
	padding: 0;
}
.navbar img{
	max-width: 100%;
	height: auto;
}
/* Struktur */
.dasgrosseganze{
	display: flex;
	flex-direction: row;
	justify-content: space-beetwen; 
	height: 100vh;
	width: 100vw;
	padding-top: 100px;
}
.sidebar{
	position: absolute;
	left: 0px;
	bottom: 0px;
	top: 70px;
	width: 150px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 1rem;
	z-index: 20;
	background: #FFF;
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.sidebar img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}

.sidebar_img{
	position: absolute;
	left: 150px;
	bottom: 0px;;
	top: 70px;
	width: 120px;
	overflow-x: hidden;
	overflow-y: auto;
	/*
    padding: 1rem;
	*/
    z-index: 20;
	background: #FFF;
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.sidebar_img img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}


.toolbar{
	position: absolute;
	right: 0px;
	bottom: 0px;
	top: 70px;
	width: 200px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 1rem;
	z-index: 20;
	background: #FFF;
	
	/*
	border-radius: 1rem;
	box-shadow: 0 0 2rem 0 rgba(136,152,170,.15);
	*/
}
.toolbar img{
	margin-bottom: 1rem;
	max-width: 100%;
	height: auto;
	border: 1px solid #eee;
}



.docviewwrapper{
	position: fixed;
	/*
    left: 304px;
	right: 150px;
	*/
    left: 270px;
	right: 200px;
    bottom: 1rem;
	top: 80px;
	/*
	right: 1rem;
	*/
}
@media screen and (min-width: 992px){
	.docview{
		display: flex;
		flex-direction: row;
		gap: 0px;
		justify-content: center;
		align-content: center;
		padding: 30px;
		height: 100%;
	}

	.page_left{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: right;
		justify-content: right;
		padding: 0px;
	}

	.page_right{
		display: flex;
		height: 100%;
		flex-direction: row;
		align-items: left;
		justify-content: left;
		padding: 0px;
	}

	.docview .page_left img{
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
		display: block;
		max-width: 100%;
		max-height: 100%;
	}

	.docview .page_right img{
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
		display: block;
		max-width: 100%;
		max-height: 100%;
	}
	.docview a{
		display: flex;
		height: 100%;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}
}
@media screen and (max-width: 992px){
	.docviewwrapper{
		overflow-x: hidden;
		overflow-y: auto;
		bottom: 0;
		top: 90px;
	}
	.docview {
		display: block;
		padding: 0 20px;
	}
	.docview a{
		display: block;
		padding: 20px;
	}
	.docview{
		flex-wrap: wrap;
	}
	.docview img{
		display: block;
		max-width: 100%;
		height: auto;
		box-shadow: 0 0 2rem 0 rgba(136,152,170,.45);
	}
} 



.spine{
    background-color: pink;
    width: 100px;
}
.imglist {
    /*
    background-color: red;
    */
    overflow-y: scroll;
    max-height: 80vh;
    padding-bottom: 0px;
    margin-left: 0px;
}

.maxih {
    background-color: red;
    max-height: 80vh;
    min-height: 80vh;
    padding-bottom: 0px;
    margin-left: 0px;
}

.photo_book_image_list_wrapper {
    padding-top: 0.5rem;
    padding-right: 0.5rem;
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.landscape {
    width: 148mm;
    height: 105mm;

    width: 222mm;
    height: 157mm;
}

.portrait {
    height: 297mm;
    width: 210mm;

    width: 157mm;
    height: 222mm;


}

.printpage {
    background-size: contain;
    background-repeat: no-repeat;
}

.page_framed {
    background-size: contain;
    background-repeat: no-repeat;
    padding: 1cm;
}

.page_contain {
    background-size: contain;
    background-repeat: no-repeat;
}

.page_cover {
    background-size: cover;
}

.flipbook-bg {
  width: 80vw;
  height: 80vh;
}




#c {
  height: 200px;
  width: 400px;
  border: 1px solid gray;
}



</style>