<template>
    <div>
        <admin-dashboard v-if="currentAccount.isAdmin()"></admin-dashboard>
        <editor-dashboard v-if="currentAccount.isEditor()"></editor-dashboard>
        <designer-dashboard v-if="currentAccount.isDesigner()"></designer-dashboard>
        <artist-dashboard v-if="currentAccount.isPhotographer()"></artist-dashboard>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';

//import RouteBreadCrumb from '@/components/Breadcrumb/RouteBreadcrumb';
import AppStyle from '@/plugins/appStyle';

//import AdminDashboard from './AdminDashboard';
//import EditorDashboard from './EditorDashboard';
//import DesignerDashboard from './DesignerDashboard';
import ArtistDashboard from './ArtistDashboard';

export default {
    mixins: [AppStyle.Mixin],
    components: {
       // RouteBreadCrumb,
       // AdminDashboard,
       // EditorDashboard,
       // DesignerDashboard,
        ArtistDashboard
    },
    data() {
        return {
            loading: false,
        };
    },
    computed: {
        ...mapGetters({ currentAccount: 'currentAccount' })
    },
    methods: {
    },
    mounted() {
    },
};
</script>