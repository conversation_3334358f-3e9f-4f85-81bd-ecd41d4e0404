<template>

<b-card>

<div class="row">
<div class="col">
<h5 class="card-title text-uppercase text-muted mb-0">{{ currentAccount.firstname }} {{ currentAccount.lastname }}</h5>
<span class="h4 font-weight-bold mb-0">Adjust your profile here</span>
</div>
<div class="col-auto">
<div class="icon icon-shape black text-white rounded-circle shadow">
  <img alt="Profilbild" :src="'data:image/jpg;base64,'+currentAccount.profile_img" style="width: 48px;">
   
</div>
</div>
</div>
<p class="mt-3 mb-0 text-sm">

<span class="text-nowrap">
    
    <button type="button" class="btn btn-sm btn-dark">Edit</button>

</span>
</p>



</b-card>
</template>

<script>
import { mapGetters } from "vuex";
import moment from 'moment'

export default {
    name: "profile-widget",
    data() {
      return {
        loading: false,
        currentBook: [],
      };
    },
    props: {
        photoBookId: '',
    },
    methods: {
      retrieveBook() {
        this.loading = true;
        this.$api.getPhotoBook(this.photoBookId)
          .then((response) => {
            console.log(response);
            this.currentBook = response.data;
            console.log("CurrentBook:"+this.currentBook);
          })
          .catch((e) => {
            console.log(e);
          }).finally(() => {
            this.loading = false
          });
      },
      formatDate(date) {
        return moment(date).format("Do MMM YYYY")
      },
    },
    computed: {
        ...mapGetters({ currentAccount: 'currentAccount' }),
        bookLoaded: function () {
            return this.currentBook ? true : false;
        }
    },
    mounted() {
      //  this.retrieveBook();
    },
};
</script>
<style></style>