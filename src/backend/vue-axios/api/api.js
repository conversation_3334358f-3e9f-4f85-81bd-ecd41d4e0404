
import photo_book from "../../../store/photo_book";

export default class Api {

  _axios = null;

  constructor(Vue, options) {
    let {
      axios,
    } = options || {
      axios: false,
    };
    this._axios = axios;
    Vue.prototype.$api = this;
    Vue.prototype.$image_host = process.env.VUE_APP_IMAGE_HOST;
  }

  updateAccount(account, params) {
    return this._axios.post('/spa/accounts/update', { account: account, params: params });
  }

  login(account, password) {
    return this._axios.post('/auth', { account: account, password: password });
  }

  getContacts(params) {
    return this._axios.get("/spa/contacts", { params });
  }
  getContact(params) {
    return this._axios.get("/spa/contacts" + params);
  }
  searchContacts(params) {
    return this._axios.get("/spa/contacts/search", { params });
  }

  agreePhotoBook(id) {
    return this._axios.post("/spa/photo_books/" + id + "/agree");
  }
  getOpenPhotoBooks(params) {
    return this._axios.get("/spa/photo_books/open", { params });
  }
  getPresalePhotoBooks(params) {
    return this._axios.get("/spa/photo_books/presale", { params });
  }
  getActivePhotoBooks(params) {
    return this._axios.get("/spa/photo_books/active", { params });
  }
  getPhotoBooks(params) {
    return this._axios.get("/spa/photo_books", { params });
  }
  getPhotoBook(params) {
    return this._axios.get("/spa/photo_books/" + params);
  }
  searchPhotoBooks(params) {
    return this._axios.get("/spa/photo_books/search", { params });
  }


  addTusPhotoBookImage(id) {
    return this._axios.get("/spa/photo_books/" + id + "/add_photo_book_image_tus", { params });
  }

  getSfPhotoBookImages(photo_book_id, params, type = "") {
    return this._axios.get("/spa/snap_forms/" + photo_book_id + "/photo_book_images?&imagetype=" + type, { params });
  }
  getSfPhotoBookImage(photo_book_id, params) {
    return this._axios.get("/spa/snap_forms/" + photo_book_id + "/photo_book_image/" + params);
  }

  positionPhotoBookImage(params) {
    this._axios.get("/spa/photo_books/" + params["pbid"] + "/photo_book_images/" + params["pbiid"] + "/position/" + params["pos"])
  }

  getPhotoBookImages(photo_book_id, params, type = "") {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/photo_book_images?&imagetype=" + type, { params });
  }
  getPhotoBookImage(photo_book_id, params) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/photo_book_image/" + params);
  }

  getPhotoBookImageOriginal(photo_book_id, params) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/photo_book_image_original/" + params);
  }

  getPhotoBookPages(photo_book_id, params) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/photo_book_pages", { params });
  }
  getPhotoBookPage(photo_book_id, params) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/photo_book_page/" + params);
  }

  getPhotoBookDesigner(photo_book_id) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/designer");
  }
  getPhotoBookComments(photo_book_id) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/comments");
  }
  getComments(commentable_type, commentable_id) {
    //return this._axios.get("/spa/comments/list",  params );
    return this._axios.get("/spa/comments?&commentable_type=" + commentable_type + "&commentable_id=" + commentable_id);
  }
  setComment(params) {
    return this._axios.post("/spa/comments/", params);
  }
  getDocuments(params) {
    return this._axios.get("/spa/documents", { params });
  }
  getDocument(params) {
    return this._axios.get("/spa/documents/" + params);
  }
  getDocumentFile(params) {
    return this._axios.get("/spa/documents/" + params + "/download", { responseType: 'blob' });
  }
  getRegistrationData(params) {
    return this._axios.get("/spa/r/" + params);
  }
  setRegistrationData(account, acctok, projtok, agree) {
    return this._axios.post("/spa/r/register", { account: account, acctok: acctok, projtok: projtok, agree: agree });
  }
  createSnapWizard(params) {
    return this._axios.post("/spa/sfw/", params);
  }
  updateSnapWizard(params, id) {
    return this._axios.post("/spa/sfw/" + id, params);
  }
  fetchSnapWizard(params, id) {
    return this._axios.post("/spa/sfw/" + id, params);
  }
  getCountries() {
    return this._axios.get("/spa/countries");
  }

  acceptCoverLayout(photo_book_id, photo_book_image_id, params) {
    return this._axios.post("/spa/photo_books/" + photo_book_id + "/accept_cover_layout/" + photo_book_image_id, params);
  }
  rejectCoverLayout(photo_book_id, photo_book_image_id, params) {
    return this._axios.post("/spa/photo_books/" + photo_book_id + "/reject_cover_layout/" + photo_book_image_id, params);
  }
  submitInterior(photo_book_id) {
    return this._axios.get("/spa/photo_books/" + photo_book_id + "/submit_interior");
  }

  getAccount() {
    return this._axios.get('/spa/accounts/current');
  }

}
